# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [<PERSON>l](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## How to Run This App

1. **Install dependencies**  
   Open your terminal and run:
   ```bash
   npm install
   ```

2. **Start the development server**  
   ```bash
   npm run dev
   ```
   This will start the app at [http://localhost:5173](http://localhost:5173) (or another port if 5173 is in use).

3. **Build for production**  
   To create a production build, run:
   ```bash
   npm run build
   ```

4. **Preview the production build**  
   After building, you can preview the production build locally:
   ```bash
   npm run preview
   ```

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript with type-aware lint rules enabled. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) for information on how to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project.
