/* App Container */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  margin: 0 0 20px 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.stat {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Add Todo Form */
.add-todo-form {
  padding: 30px;
  border-bottom: 1px solid #f0f0f0;
}

.input-group {
  display: flex;
  gap: 15px;
  max-width: 600px;
  margin: 0 auto;
}

.todo-input {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  outline: none;
}

.todo-input:focus {
  border-color: #4facfe;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.add-btn {
  padding: 15px 25px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Todo List */
.todo-list {
  padding: 30px;
}

.todo-section {
  margin-bottom: 30px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
}

.todos {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Todo Item */
.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.todo-item:hover {
  border-color: #e1e5e9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.todo-item.completed {
  background: #e8f5e8;
  border-color: #c3e6c3;
}

.todo-content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

/* Custom Checkbox */
.checkbox-container {
  position: relative;
  cursor: pointer;
  user-select: none;
}

.todo-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  height: 24px;
  width: 24px;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 6px;
  display: inline-block;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-container:hover .checkmark {
  border-color: #4facfe;
}

.todo-checkbox:checked ~ .checkmark {
  background-color: #4facfe;
  border-color: #4facfe;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 7px;
  top: 3px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.todo-checkbox:checked ~ .checkmark:after {
  display: block;
}

/* Todo Text */
.todo-text-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.todo-text {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #888;
}

.todo-date {
  font-size: 12px;
  color: #888;
  font-style: italic;
}

/* Delete Button */
.delete-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.delete-btn:hover {
  background: #ffebee;
  opacity: 1;
  transform: scale(1.1);
}

/* Actions */
.actions {
  padding: 0 30px 30px;
  text-align: center;
}

.clear-completed-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-completed-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

/* Empty State */
.empty-state {
  padding: 60px 30px;
  text-align: center;
}

.empty-state p {
  font-size: 18px;
  color: #888;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .container {
    border-radius: 15px;
  }

  .header {
    padding: 20px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .stats {
    gap: 10px;
  }

  .stat {
    padding: 6px 12px;
    font-size: 14px;
  }

  .add-todo-form {
    padding: 20px;
  }

  .input-group {
    flex-direction: column;
    gap: 12px;
  }

  .todo-input {
    padding: 12px 16px;
    font-size: 16px;
  }

  .add-btn {
    padding: 12px 20px;
  }

  .todo-list {
    padding: 20px;
  }

  .todo-item {
    padding: 15px;
  }

  .todo-text {
    font-size: 15px;
  }

  .actions {
    padding: 0 20px 20px;
  }

  .empty-state {
    padding: 40px 20px;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.8rem;
  }

  .stats {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .todo-item {
    padding: 12px;
  }

  .todo-content {
    gap: 10px;
  }

  .todo-text {
    font-size: 14px;
  }

  .delete-btn {
    font-size: 16px;
    padding: 6px;
  }
}
